import { NextRequest } from 'next/server';
import { getChatCompletionStream, ChatMessage } from '@/functions/openai';

// Interface for frontend message format
interface FrontendMessage {
  id: number;
  text: string;
  sender: 'user' | 'bot';
  image?: string; // base64 encoded image
}

// Convert frontend messages to OpenAI format
function convertToOpenAIMessages(frontendMessages: FrontendMessage[]): ChatMessage[] {
  const openAIMessages: ChatMessage[] = [
    {
      role: 'system',
      content: 'You are a helpful assistant. Provide concise and helpful responses. When analyzing images, be detailed and accurate in your descriptions.'
    }
  ];

  frontendMessages.forEach(msg => {
    if (msg.sender === 'user') {
      if (msg.image) {
        // Multimodal message with image
        openAIMessages.push({
          role: 'user',
          content: [
            {
              type: 'text',
              text: msg.text
            },
            {
              type: 'image_url',
              image_url: {
                url: `data:image/jpeg;base64,${msg.image}`,
                detail: 'high'
              }
            }
          ]
        });
      } else {
        // Text-only user message
        openAIMessages.push({
          role: 'user',
          content: msg.text
        });
      }
    } else if (msg.sender === 'bot') {
      // Assistant message
      openAIMessages.push({
        role: 'assistant',
        content: msg.text
      });
    }
  });

  return openAIMessages;
}

export async function POST(request: NextRequest) {
  try {
    const { message, image, conversationHistory } = await request.json();

    if (!message || typeof message !== 'string') {
      return new Response(
        JSON.stringify({ error: 'Message is required and must be a string' }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    let messages: ChatMessage[];

    if (conversationHistory && Array.isArray(conversationHistory)) {
      // Use conversation history if provided
      messages = convertToOpenAIMessages(conversationHistory);
    } else {
      // Fallback to single message (backward compatibility)
      messages = [
        {
          role: 'system',
          content: 'You are a helpful assistant. Provide concise and helpful responses. When analyzing images, be detailed and accurate in your descriptions.'
        }
      ];
    }

    // Add the current message
    if (image) {
      messages.push({
        role: 'user',
        content: [
          {
            type: 'text',
            text: message
          },
          {
            type: 'image_url',
            image_url: {
              url: `data:image/jpeg;base64,${image}`,
              detail: 'high'
            }
          }
        ]
      });
    } else {
      // Text-only message
      messages.push({
        role: 'user',
        content: message
      });
    }

    const stream = await getChatCompletionStream(messages);

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  } catch (error) {
    console.error('Chat streaming API error:', error);
    return new Response(
      JSON.stringify({ error: 'Failed to process chat request' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
