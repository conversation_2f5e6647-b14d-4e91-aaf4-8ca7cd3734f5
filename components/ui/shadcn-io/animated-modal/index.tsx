"use client";

import { cn } from '@/lib/utils';
import { useOnClickOutside } from '@/hooks/use-on-click-outside';
import { AnimatePresence, motion, Variants } from 'framer-motion';
import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useRef,
  useState
} from 'react';

// Types
interface ModalContextType {
  open: boolean;
  setOpen: (open: boolean) => void;
}

interface ModalComponentProps {
  children: ReactNode;
  className?: string;
}

// Animation variants
const overlayVariants: Variants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, backdropFilter: "blur(10px)" },
  exit: { opacity: 0, backdropFilter: "blur(0px)" }
};

const modalVariants: Variants = {
  hidden: { opacity: 0, scale: 0.5, rotateX: 40, y: 40 },
  visible: { opacity: 1, scale: 1, rotateX: 0, y: 0 },
  exit: { opacity: 0, scale: 0.8, rotateX: 10 }
};

const modalTransition = {
  type: "spring" as const,
  stiffness: 260,
  damping: 15
};

// Context
const ModalContext = createContext<ModalContextType | undefined>(undefined);

export const useModal = () => {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error("useModal must be used within a ModalProvider");
  }
  return context;
};

// Provider and Root Component
export const ModalProvider = ({ children }: { children: ReactNode }) => {
  const [open, setOpen] = useState(false);

  return (
    <ModalContext.Provider value={{ open, setOpen }}>
      {children}
    </ModalContext.Provider>
  );
};

export function Modal({ children }: { children: ReactNode }) {
  return <ModalProvider>{children}</ModalProvider>;
}

// Components
export const ModalTrigger = ({ children, className }: ModalComponentProps) => {
  const { setOpen } = useModal();

  return (
    <button
      className={cn(
        "px-4 py-2 rounded-md text-black dark:text-white text-center relative overflow-hidden",
        className
      )}
      onClick={() => setOpen(true)}
    >
      {children}
    </button>
  );
};

export const ModalBody = ({ children, className }: ModalComponentProps) => {
  const { open, setOpen } = useModal();
  const modalRef = useRef<HTMLDivElement>(null);

  // Handle body scroll lock
  useEffect(() => {
    document.body.style.overflow = open ? "hidden" : "auto";
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [open]);

  // Handle click outside
  useOnClickOutside(modalRef as React.RefObject<HTMLElement>, () => setOpen(false));

  return (
    <AnimatePresence>
      {open && (
        <motion.div
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="fixed [perspective:800px] [transform-style:preserve-3d] inset-0 h-full w-full flex items-center justify-center z-50"
        >
          <Overlay />
          <motion.div
            ref={modalRef}
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            transition={modalTransition}
            className={cn(
              "min-h-[50%] max-h-[90%] md:max-w-[40%] bg-white dark:bg-neutral-950 border border-transparent dark:border-neutral-800 md:rounded-2xl relative z-50 flex flex-col flex-1 overflow-hidden",
              className
            )}
          >
            <CloseIcon />
            {children}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export const ModalContent = ({ children, className }: ModalComponentProps) => {
  return (
    <div className={cn("flex flex-col flex-1 p-8 md:p-10", className)}>
      {children}
    </div>
  );
};

export const ModalFooter = ({ children, className }: ModalComponentProps) => {
  return (
    <div
      className={cn(
        "flex justify-end p-4 bg-gray-100 dark:bg-neutral-900",
        className
      )}
    >
      {children}
    </div>
  );
};

// Internal Components
const Overlay = ({ className }: { className?: string }) => {
  return (
    <motion.div
      variants={overlayVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      className={cn("fixed inset-0 h-full w-full bg-black bg-opacity-50 z-40", className)}
    />
  );
};

const CloseIcon = () => {
  const { setOpen } = useModal();

  return (
    <button
      onClick={() => setOpen(false)}
      className="absolute top-4 right-4 group"
      aria-label="Close modal"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="text-black dark:text-white h-4 w-4 group-hover:scale-125 group-hover:rotate-3 transition duration-200"
      >
        <path d="M18 6l-12 12" />
        <path d="M6 6l12 12" />
      </svg>
    </button>
  );
};

