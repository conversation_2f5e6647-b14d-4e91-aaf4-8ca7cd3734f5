<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Upload</title>
</head>
<body>
    <h1>Test Image Upload to Chat API</h1>

    <div id="dragDropArea" style="
        border: 2px dashed #ccc;
        padding: 40px;
        margin: 20px 0;
        text-align: center;
        background: #f9f9f9;
        transition: all 0.3s ease;
    ">
        <p>Drag and drop an image here, or click to select</p>
        <input type="file" id="image" accept="image/*" style="display: none;" />
    </div>

    <form id="testForm">
        <div>
            <label for="message">Message:</label>
            <input type="text" id="message" value="What do you see in this image?" />
        </div>
        <button type="submit">Send Test</button>
    </form>

    <div id="result"></div>

    <script>
        const dragDropArea = document.getElementById('dragDropArea');
        const imageInput = document.getElementById('image');

        // Drag and drop functionality
        dragDropArea.addEventListener('click', () => imageInput.click());

        dragDropArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            dragDropArea.style.background = '#e3f2fd';
            dragDropArea.style.borderColor = '#2196f3';
        });

        dragDropArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dragDropArea.style.background = '#f9f9f9';
            dragDropArea.style.borderColor = '#ccc';
        });

        dragDropArea.addEventListener('drop', (e) => {
            e.preventDefault();
            dragDropArea.style.background = '#f9f9f9';
            dragDropArea.style.borderColor = '#ccc';

            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type.startsWith('image/')) {
                imageInput.files = files;
                dragDropArea.innerHTML = `<p>Selected: ${files[0].name}</p>`;
            }
        });

        imageInput.addEventListener('change', (e) => {
            if (e.target.files[0]) {
                dragDropArea.innerHTML = `<p>Selected: ${e.target.files[0].name}</p>`;
            }
        });

        document.getElementById('testForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const messageInput = document.getElementById('message');
            const resultDiv = document.getElementById('result');

            if (!imageInput.files[0]) {
                alert('Please select an image');
                return;
            }

            try {
                // Convert image to base64
                const file = imageInput.files[0];
                const reader = new FileReader();

                reader.onload = async () => {
                    const base64 = reader.result.split(',')[1]; // Remove data URL prefix

                    const response = await fetch('http://localhost:3001/api/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: messageInput.value,
                            image: base64,
                            conversationHistory: [
                                {
                                    id: 1,
                                    text: "Hello! How can I help you today?",
                                    sender: "bot"
                                },
                                {
                                    id: 2,
                                    text: "I'm going to send you an image",
                                    sender: "user"
                                },
                                {
                                    id: 3,
                                    text: "Great! I'm ready to analyze any image you share with me.",
                                    sender: "bot"
                                }
                            ]
                        })
                    });

                    const data = await response.json();

                    if (response.ok) {
                        resultDiv.innerHTML = `<h3>Success!</h3><p>${data.response}</p>`;
                    } else {
                        resultDiv.innerHTML = `<h3>Error!</h3><p>${data.error}</p>`;
                    }
                };

                reader.readAsDataURL(file);

            } catch (error) {
                resultDiv.innerHTML = `<h3>Error!</h3><p>${error.message}</p>`;
            }
        });
    </script>
</body>
</html>
