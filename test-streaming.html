<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Streaming Chat Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #fafafa;
            border-radius: 4px;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            max-width: 70%;
        }
        .user {
            background-color: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        .bot {
            background-color: #e9ecef;
            color: #333;
        }
        .input-container {
            display: flex;
            gap: 10px;
        }
        input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .typing {
            font-style: italic;
            color: #666;
        }
        .streaming-text {
            border-right: 2px solid #007bff;
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0%, 50% { border-color: #007bff; }
            51%, 100% { border-color: transparent; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Streaming Chat Test</h1>
        <div id="messages" class="messages">
            <div class="message bot">
                <strong>Bot:</strong> Hello! I'm ready to chat with streaming responses. Ask me anything!
            </div>
        </div>
        <div class="input-container">
            <input type="text" id="messageInput" placeholder="Type your message..." />
            <button id="sendButton" onclick="sendMessage()">Send</button>
        </div>
    </div>

    <script>
        let conversationHistory = [
            { id: 1, text: "Hello! I'm ready to chat with streaming responses. Ask me anything!", sender: "bot" }
        ];
        let isStreaming = false;

        function addMessage(text, sender, isStreaming = false) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const content = `<strong>${sender === 'user' ? 'You' : 'Bot'}:</strong> <span class="message-text ${isStreaming ? 'streaming-text' : ''}">${text}</span>`;
            messageDiv.innerHTML = content;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
            
            return messageDiv;
        }

        function updateMessage(messageDiv, text, isStreaming = false) {
            const textSpan = messageDiv.querySelector('.message-text');
            textSpan.textContent = text;
            textSpan.className = `message-text ${isStreaming ? 'streaming-text' : ''}`;
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            const message = input.value.trim();
            
            if (!message || isStreaming) return;
            
            // Add user message
            addMessage(message, 'user');
            conversationHistory.push({ id: Date.now(), text: message, sender: 'user' });
            
            // Clear input and disable button
            input.value = '';
            sendButton.disabled = true;
            sendButton.textContent = 'Sending...';
            isStreaming = true;
            
            // Add placeholder bot message
            const botMessageDiv = addMessage('', 'bot', true);
            let accumulatedText = '';
            
            try {
                const response = await fetch('http://localhost:3002/api/chat/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        conversationHistory: conversationHistory
                    }),
                });

                if (!response.ok) {
                    throw new Error('Failed to get response');
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                if (data.content) {
                                    accumulatedText += data.content;
                                    updateMessage(botMessageDiv, accumulatedText, true);
                                } else if (data.done) {
                                    updateMessage(botMessageDiv, accumulatedText, false);
                                    conversationHistory.push({ 
                                        id: Date.now(), 
                                        text: accumulatedText, 
                                        sender: 'bot' 
                                    });
                                    break;
                                } else if (data.error) {
                                    throw new Error(data.error);
                                }
                            } catch (parseError) {
                                // Ignore parsing errors for incomplete chunks
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('Error:', error);
                updateMessage(botMessageDiv, 'Sorry, I encountered an error. Please try again.', false);
            } finally {
                isStreaming = false;
                sendButton.disabled = false;
                sendButton.textContent = 'Send';
            }
        }

        // Allow Enter key to send message
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !isStreaming) {
                sendMessage();
            }
        });
    </script>
</body>
</html>
